import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * @description 登录
 * @param phone 手机号
 * @param password 密码
 * @returns 
 */
export function fetchLogin(phone: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/login',
    method: 'post',
    data: {
      phone,
      password
    }
  });
}

/**
 * @description 发送注册验证码
 * @param phone 手机号
 * @returns 
 */
export function fetchSendRegisterSms(phone: string, type: string) {
  return request<Api.RegisterSms.RegisterSmsResponse>({ 
    url: '/api/v1/send-sms',
    method: 'post',
    data: {
      phone,
      type
    }
  });
}

/**
 * @description 注册
 * @param userName 用户名
 * @param code 短信验证码
 * @param password 密码
 * @param confirmPassword 确认密码
 * @returns
 */
export function fetchRegister(nickname: string, phone: string, sms_code: string, password: string) {
  return request<Api.Register.RegisterResponse>({
    url: '/api/v1/register',
    method: 'post',
    data: {
      nickname,
      phone,
      sms_code,
      password
    }
  });
}

/**
 * @description 忘记密码 - 发送验证码
 * @param phone 手机号
 * @param type 类型
 * @returns
 */
export function fetchSendResetPasswordSms(phone: string, type: string) {
  return request<Api.ResetPasswordSms.ResetPasswordSmsResponse>({
    url: '/api/v1/send-sms',
    method: 'post',
    data: {
      phone,
      type
    }
  });
}

/**
 * @description 重置密码
 * @param phone 手机号
 * @param sms_code 短信验证码
 * @param new_password 新密码
 * @returns
 */
export function fetchResetPassword(phone: string, sms_code: string, new_password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/forgot-password',
    method: 'post',
    data: {
      phone,
      sms_code,
      new_password
    }
  });
}

/**
 * @description 获取用户信息
 */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ 
    url: '/api/v1/user/profile',
    method: 'get'
  });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * @description 获取用户数据列表（分页）
 * @param page 当前页数
 * @param page_size 每页大小
 * @param role 角色（固定为user）
 * @param is_active 是否激活（固定为1）
 * @returns
 */
export function fetchUserData(page: number = 1, page_size: number = 20, role: string = 'user', is_active: number = 1) {
  return request<Api.Auth.UserListResponse>({
    url: `/api/v1/admin/users/?page=${page}&page_size=${page_size}&role=${role}&is_active=${is_active}`,
    method: 'get'
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
