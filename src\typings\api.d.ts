import Register from '@/views/_builtin/login/modules/register.vue';
import { namespace } from 'naive-ui/es/_utils/cssr';

/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
    }

    interface UserInfo {
      //余额
      balance: number;
      //创建时间
      created_at: string;
      //用户id
      id: number;
      //是否激活
      is_active: number;
      //昵称
      nickname: string;
      //手机号
      phone: string;
      //角色身份
      role: string[];
      //更新时间
      updated_at: string;
    }

    /** 用户列表项 */
    interface UserListItem {
      //用户id
      id: number;
      //手机号码
      phone: string;
      //角色身份
      role: string;
      //昵称
      nickname: string;
      //余额
      balance: string;
      //是否激活
      is_active: number;
      //登录失败次数
      login_failed_count: number;
      //登录冻结时间
      login_frozen_until: string;
      //是否必须修改密码
      must_change_password: boolean;
      //短信失败次数
      sms_failed_count: number;
      //短信冻结时间
      sms_frozen_until: string;
      created_at: string;
      updated_at: string;

      //后端返回数据结构
      // "id": 11,
      // "phone": "13200000000",
      // "role": "user",
      // "nickname": "qqq",
      // "balance": 0,
      // "is_active": 1,
      // "login_failed_count": 0,
      // "login_frozen_until": null,
      // "must_change_password": false,
      // "sms_failed_count": 0,
      // "sms_frozen_until": null,
      // "created_at": "2025-06-16T12:56:44.793+08:00",
      // "updated_at": "2025-06-16T04:58:13+08:00"
    }

    /** 用户列表响应 */
    interface UserListResponse {
      /** 用户列表数据 */
      list: UserListItem[];
      /** 当前页码 */
      page: number;
      /** 每页大小 */
      page_size: number;
      /** 总数量 */
      total: number;
    }
  }

  /**
   * @description 注册验证码参数
   */
  namespace RegisterSms {
    interface RegisterSmsResponse {
      code: string;
    }
    interface RegisterSmsParams {
      phone: string;
    }
  }

  /**
   * @description 重置密码验证码参数
   */
  namespace ResetPasswordSms {
    interface ResetPasswordSmsResponse {
      code: string;
      token: string;
    }
    interface ResetPasswordSmsParams {
      phone: string;
    }
  }

  /**
   * @description 注册参数
   */
  namespace Register {
    interface RegisterResponse {
      id: number;
      phone: string;
      nickname: string;
      is_active: number;
      status: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }
}
